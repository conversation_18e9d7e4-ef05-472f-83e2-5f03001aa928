<div class="comic-card upload-container">
  <h2 class="comic-subtitle">📚 Upload Your Comic!</h2>
  
  <!-- File Upload Area -->
  <div 
    class="upload-area"
    [class.drag-over]="dragOver"
    [class.has-file]="selectedFile"
    (dragover)="onDragOver($event)"
    (dragleave)="onDragLeave($event)"
    (drop)="onDrop($event)"
    (click)="fileInput.click()"
  >
    <input 
      #fileInput
      type="file" 
      accept="image/*" 
      (change)="onFileSelected($event)"
      style="display: none;"
    >
    
    <div *ngIf="!selectedFile" class="upload-placeholder">
      <div class="upload-icon">📸</div>
      <p class="upload-text">
        <strong>Click to upload</strong> or drag and drop your comic image here
      </p>
      <p class="upload-hint">
        Supports: JPEG, PNG, WebP (Max 10MB)
      </p>
    </div>
    
    <div *ngIf="selectedFile" class="file-preview">
      <div class="file-info">
        <div class="file-icon">🖼️</div>
        <div class="file-details">
          <p class="file-name">{{ selectedFile.name }}</p>
          <p class="file-size">{{ getFileSize(selectedFile.size) }}</p>
        </div>
        <button 
          type="button" 
          class="remove-file-btn"
          (click)="removeFile(); $event.stopPropagation()"
        >
          ❌
        </button>
      </div>
    </div>
  </div>

  <!-- Language Selection -->
  <div class="language-selection">
    <div class="language-group">
      <label class="comic-label">
        <span class="label-icon">🌍</span>
        Source Language (Original)
      </label>
      <select 
        class="comic-select" 
        [(ngModel)]="sourceLanguage"
      >
        <option *ngFor="let lang of supportedLanguages" [value]="lang">
          {{ lang }}
        </option>
      </select>
    </div>

    <div class="arrow-container">
      <div class="translation-arrow">➡️</div>
    </div>

    <div class="language-group">
      <label class="comic-label">
        <span class="label-icon">🎯</span>
        Target Language (Translate to)
      </label>
      <select 
        class="comic-select" 
        [(ngModel)]="targetLanguage"
      >
        <option *ngFor="let lang of supportedLanguages" [value]="lang">
          {{ lang }}
        </option>
      </select>
    </div>
  </div>

  <!-- Upload Button -->
  <div class="upload-actions">
    <button 
      class="comic-btn comic-btn-success upload-btn"
      [disabled]="!selectedFile || isUploading"
      (click)="uploadImage()"
    >
      <span *ngIf="!isUploading">🚀 Start Translation!</span>
      <span *ngIf="isUploading" class="loading-content">
        <div class="comic-loading"></div>
        Uploading...
      </span>
    </button>
  </div>

  <!-- Tips -->
  <div class="comic-bubble tips-bubble">
    <h4>💡 Pro Tips:</h4>
    <ul>
      <li>Use high-quality images for better text detection</li>
      <li>Make sure text is clearly visible and not too small</li>
      <li>Avoid images with too much background noise</li>
      <li>Single page works better than multiple pages</li>
    </ul>
  </div>
</div>
